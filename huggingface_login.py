import torch
import os
from transformers import Wav2Vec2Processor, Wav2Vec2ForCTC
from huggingface_hub import login

login("*************************************")

class ModelPusher:
    def __init__(self, device, huggingface_folder, model_path, repo_name) -> None:
        self.device = device
        self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
        self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(self.device)
        if model_path is not None:
            self.preload_model(model_path)
        self.push_to_hub(repo_name)

    def preload_model(self, model_path) -> None:
        assert os.path.exists(model_path), f"The file {model_path} does not exist. Please check the path."
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint["model"], strict=True)
        print(f"Model preloaded successfully from {model_path}.")

    def push_to_hub(self, repo_name) -> None:
        self.processor.push_to_hub(repo_name)
        self.model.push_to_hub(repo_name)
        print(f"Model and processor pushed successfully to {repo_name}.")

# Directly pass the parameters
device = "cuda" if torch.cuda.is_available() else "cpu"
repo_name = "Quran-ASR-Parah-01"
huggingface_folder = "huggingface-Parah-01"
model_path = "Parah-01/Quran-ASR/checkpoints/best_model.tar"

model_pusher = ModelPusher(
    repo_name=repo_name,
    device=device,
    huggingface_folder=huggingface_folder,
    model_path=model_path
)
