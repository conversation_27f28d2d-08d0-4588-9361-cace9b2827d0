import os
from pydub import AudioSegment

# Set the transcript file path
transcript_file = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/parah-29/test.txt"

# Initialize a variable to store the total duration
total_duration = 0

# Initialize a variable to store the maximum duration
max_duration = 0
max_duration_file = None

# Read the paths from the transcript file
with open(transcript_file, 'r', encoding='utf-8') as file:
    lines = file.readlines()

# Process each line in the transcript file
for line in lines:
    # Split the line by the pipe character
    parts = line.strip().split('|')

    # Extract the audio file path
    audio_path = parts[0].strip()

    # Check if the file is a WAV file
    if audio_path.endswith(".wav"):
        # Load the WAV file
        audio = AudioSegment.from_wav(audio_path)

        # Get the duration of the audio file
        duration = len(audio)

        # Add the duration of the audio file to the total duration
        total_duration += duration

        # Check if this audio file has the maximum duration
        if duration > max_duration:
            max_duration = duration
            max_duration_file = audio_path

# Convert the total duration from milliseconds to hours
total_hours = total_duration / (1000 * 60 * 60)

# Convert the maximum duration from milliseconds to seconds
max_duration_seconds = max_duration / 1000

print(f"Total hours of the dataset: {total_hours:.2f}")
print(f"Maximum duration of an audio file: {max_duration_seconds:.2f} seconds")
print(f"File with the maximum duration: {max_duration_file}")
