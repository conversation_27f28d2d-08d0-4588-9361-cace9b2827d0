import os

def filter_and_remove_transcripts(source_transcript, target_transcript, target_folders):
    with open(source_transcript, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    filtered_lines = []
    remaining_lines = []

    for line in lines:
        path, transcript = line.strip().split('|')
        folder_name = os.path.basename(os.path.dirname(path))
        if folder_name in target_folders:
            filtered_lines.append(line)
        else:
            remaining_lines.append(line)
    
    with open(target_transcript, 'w', encoding='utf-8') as f:
        f.writelines(filtered_lines)
    
    with open(source_transcript, 'w', encoding='utf-8') as f:
        f.writelines(remaining_lines)

# Define the target folders
target_folders = [
    "Alafasy_64kbps",
    "Banna_32kbps"
    # "<PERSON><PERSON><PERSON><PERSON><PERSON>_As-Sudais_64kbps",
    # "Ali_Jaber_64kbps",
    # "Ghamadi_40kbps",
    # "<PERSON>af<PERSON>_Ismail_128kbps",
    # "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>asim_192kbps",
    # "Yasser_Ad-Dussary_128kbps"
]

# Specify the source transcript file path
source_transcript = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/test - Copy.txt"  # Replace with the actual path

# Specify the target transcript file path
target_transcript = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/10-test.txt"  # Replace with the actual path

filter_and_remove_transcripts(source_transcript, target_transcript, target_folders)
