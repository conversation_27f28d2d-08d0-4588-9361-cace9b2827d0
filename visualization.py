import librosa
import numpy as np
import os

# Define the path to the folder containing the audio files
folder_path = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/train_dataset_wav"

# Initialize variables to store the minimum, maximum, and total values
global_min_value = np.inf
global_max_value = -np.inf
total_value = 0
num_samples = 0

min_values = []
max_values = []

# Loop over all subfolders in the folder
for subfolder in os.listdir(folder_path):
    subfolder_path = os.path.join(folder_path, subfolder)
    # Loop over all audio files in the subfolder
    for filename in os.listdir(subfolder_path):
        if filename.endswith('.wav'):  # Only process .wav files
            file_path = os.path.join(subfolder_path, filename)
            # Load the audio file
            audio, sr = librosa.load(file_path)
            # Calculate the minimum, maximum, and total values for the current audio file
            current_min = np.min(audio)
            current_max = np.max(audio)
            min_values.append(current_min)
            max_values.append(current_max)
            
            # Update global min and max
            global_min_value = min(global_min_value, current_min)
            global_max_value = max(global_max_value, current_max)
            
            # Update the total value and number of samples
            total_value += np.sum(audio)
            num_samples += len(audio)

# Calculate the average value of the entire dataset
avg_value = total_value / num_samples

# Calculate the average of the min values and the average of the max values
min_avg = np.mean(min_values)
max_avg = np.mean(max_values)

# Print the values
print(f'Min value (overall): {global_min_value}')
print(f'Max value (overall): {global_max_value}')
print(f'Average value (overall): {avg_value}')
print(f'Average of min values (min_avg): {min_avg}')
print(f'Average of max values (max_avg): {max_avg}')





# "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/train_dataset_wav/52/8-1.wav"