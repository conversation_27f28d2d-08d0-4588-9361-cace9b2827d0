# Technical Appendix: Implementation Details and Code Specifications

## A.1 Model Configuration Details

### A.1.1 Complete Configuration File Structure

```toml
[meta]
name = "Quran-ASR" 
pretrained_path = "elgeish/wav2vec2-large-xlsr-53-arabic"
seed = 42
epochs = 15
save_dir = "E:/Quran Trained/10_full_quran/"
gradient_accumulation_steps = 2
use_amp = true
device_ids = "0"
sr = 16000
max_clip_grad_norm = 5.0

[special_tokens]
bos_token = "<bos>"
eos_token = "<eos>"
unk_token = "<unk>"
pad_token = "<pad>"

[train_dataset]
path = "base.base_dataset.BaseDataset"
    [train_dataset.args]
    path = "path/to/train.txt"
    preload_data = false
    delimiter = "|"
    nb_workers = 16
    
    [train_dataset.dataloader]
    batch_size = 2
    num_workers = 16
    pin_memory = true 
    drop_last = true

    [train_dataset.sampler]
    shuffle = true 
    drop_last = true

[val_dataset]
path = "base.base_dataset.BaseDataset"
    [val_dataset.args]
    path = "path/to/test.txt"
    preload_data = false
    delimiter = "|"
    nb_workers = 16

    [val_dataset.dataloader]
    batch_size = 1
    num_workers = 4

    [val_dataset.sampler]
    shuffle = false 
    drop_last = false

[optimizer]
lr = 1e-6

[scheduler] 
max_lr = 5e-4

[trainer]
path = "trainer.trainer.Trainer"
    [trainer.args]
    validation_interval = 5000
    save_max_metric_score = false
```

### A.1.2 Model Architecture Specifications

#### Wav2Vec2 Large XLSR-53 Arabic Model Details
- **Base Model**: `elgeish/wav2vec2-large-xlsr-53-arabic`
- **Parameters**: ~317M parameters
- **Feature Encoder**: 7 convolutional layers
- **Transformer Layers**: 24 layers
- **Attention Heads**: 16
- **Hidden Size**: 1024
- **Feed-forward Size**: 4096
- **Vocabulary Size**: Dynamic (Arabic character set)

## A.2 Data Processing Pipeline

### A.2.1 Audio Preprocessing Functions

```python
def load_wav(path, sr):
    """Load and resample audio to target sampling rate"""
    return librosa.load(path, sr=sr)[0]

def subsample(data, sub_sample_length):
    """Subsample or pad audio to fixed length"""
    assert np.ndim(data) == 1, f"Only support 1D data. The dim is {np.ndim(data)}"
    length = len(data)

    if length > sub_sample_length:
        start = np.random.randint(length - sub_sample_length)
        end = start + sub_sample_length
        data = data[start:end]
        assert len(data) == sub_sample_length
        return data
    elif length < sub_sample_length:
        data = np.append(data, np.zeros(sub_sample_length - length, dtype=np.float32))
        assert len(data) == sub_sample_length
        return data
    else:
        return data
```

### A.2.2 Text Preprocessing Implementation

```python
class BaseDataset(Dataset):
    def __init__(self, rank, dist, path, sr, delimiter, special_tokens, 
                 min_duration=-np.inf, max_duration=np.inf, preload_data=False, 
                 transform=None, nb_workers=4):
        self.rank = rank
        self.dist = dist
        self.sr = sr
        # Special characters to remove in your data
        self.chars_to_ignore = r'[,?.!\-;:"""%\']'
        self.transform = transform
        self.preload_data = preload_data
        self.min_duration = min_duration
        self.max_duration = max_duration
        self.df = self.load_data(path, delimiter)
        self.special_tokens = special_tokens

    def remove_special_characters(self, transcript) -> str:
        """Remove special characters and normalize text"""
        transcript = re.sub(self.chars_to_ignore, '', transcript).lower()
        return transcript

    def get_vocab_dict(self) -> Dict[int, str]:
        """Generate vocabulary dictionary from training data"""
        all_text = " ".join(list(self.df["transcript"]))
        # Remove special tokens in all_text
        for v in self.special_tokens.values():
            all_text = all_text.replace(v, '')
        vocab_list = list(set(all_text))
        vocab_list.sort()
        vocab_dict = {v: k for k, v in enumerate(vocab_list)}

        vocab_dict["|"] = vocab_dict[" "]
        del vocab_dict[" "]
        for v in self.special_tokens.values():
            vocab_dict[v] = len(vocab_dict)
        return vocab_dict
```

## A.3 Training Implementation Details

### A.3.1 Distributed Training Setup

```python
def setup(rank, world_size):
    """Initialize distributed training environment"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '4444'
    dist.init_process_group("gloo", rank=rank, world_size=world_size, 
                          timeout=datetime.timedelta(seconds=3600 * 5))

def cleanup():
    """Clean up distributed training resources"""
    dist.destroy_process_group()
```

### A.3.2 Model Initialization and Configuration

```python
def initialize_model(config, processor):
    """Initialize Wav2Vec2 model with CTC head"""
    model = Wav2Vec2ForCTC.from_pretrained(
        config['meta']['pretrained_path'],
        ctc_loss_reduction="mean",
        pad_token_id=processor.tokenizer.pad_token_id,
        vocab_size=len(processor.tokenizer),
        gradient_checkpointing=False,
        ignore_mismatched_sizes=True
    )
    
    # Freeze feature encoder for small datasets
    model.freeze_feature_encoder()
    
    return model
```

### A.3.3 DataLoader Configuration

```python
def create_dataloaders(config, train_ds, val_ds, default_collate, rank, world_size):
    """Create training and validation dataloaders with distributed sampling"""
    
    # Training dataloader
    train_sampler = torch.utils.data.distributed.DistributedSampler(
        train_ds,
        num_replicas=world_size,
        rank=rank,
        **config["train_dataset"]["sampler"]
    )
    train_dl = DataLoader(
        dataset=train_ds,
        **config["train_dataset"]["dataloader"],
        sampler=train_sampler,
        collate_fn=default_collate
    )

    # Validation dataloader
    val_sampler = torch.utils.data.distributed.DistributedSampler(
        val_ds,
        num_replicas=world_size,
        rank=rank,
        **config["val_dataset"]["sampler"]
    )
    val_dl = DataLoader(
        dataset=val_ds,
        **config["val_dataset"]["dataloader"],
        sampler=val_sampler,
        collate_fn=default_collate
    )
    
    return train_dl, val_dl, train_sampler, val_sampler
```

## A.4 Training Loop Implementation

### A.4.1 Main Training Function

```python
def main(rank, world_size, config, resume, preload):
    """Main training function with distributed setup"""
    os.environ['CUDA_VISIBLE_DEVICES'] = config["meta"]["device_ids"]
    os.environ['TORCH_DISTRIBUTED_DEBUG'] = 'INFO'
    setup(rank, world_size)

    # Configuration extraction
    epochs = config["meta"]["epochs"]
    gradient_accumulation_steps = config["meta"]["gradient_accumulation_steps"]
    use_amp = config["meta"]["use_amp"]
    max_clip_grad_norm = config["meta"]["max_clip_grad_norm"]
    
    # Directory setup
    save_dir = os.path.join(config["meta"]["save_dir"], 
                           config["meta"]['name'] + '/checkpoints')
    log_dir = os.path.join(config["meta"]["save_dir"], 
                          config["meta"]['name'] + '/log_dir')

    if rank == 0:
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)

    # Seed setting for reproducibility
    config["meta"]["seed"] += rank
    set_seed(config["meta"]["seed"])
    
    # Dataset initialization
    train_base_ds = initialize_module(config["train_dataset"]["path"], 
                                    args=config["train_dataset"]["args"])
    vocab_dict = train_base_ds.get_vocab_dict()
    
    # Save vocabulary
    with open('vocab.json', 'w+') as f:
        json.dump(vocab_dict, f)
    
    dist.barrier()
    
    # Processor initialization
    tokenizer = Wav2Vec2CTCTokenizer("vocab.json",
                                    **config["special_tokens"],
                                    word_delimiter_token="|")
    feature_extractor = Wav2Vec2FeatureExtractor.from_pretrained(
        config['meta']['pretrained_path'])
    processor = Wav2Vec2Processor(feature_extractor=feature_extractor, 
                                 tokenizer=tokenizer)
    default_collate = DefaultCollate(processor, config['meta']['sr'])

    # Model initialization
    model = initialize_model(config, processor)
    model = DDP(model.to(rank), device_ids=[rank], find_unused_parameters=True)

    # Training components
    compute_metric = Metric(processor)
    optimizer = torch.optim.AdamW(
        params=model.parameters(),
        lr=config["optimizer"]["lr"]
    )
    
    steps_per_epoch = (len(train_dl)//gradient_accumulation_steps) + \
                     (len(train_dl)%gradient_accumulation_steps != 0)
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config["scheduler"]["max_lr"],
        epochs=epochs,
        steps_per_epoch=steps_per_epoch
    )

    # Trainer initialization and training
    trainer_class = initialize_module(config["trainer"]["path"], initialize=False)
    trainer = trainer_class(
        dist=dist, rank=rank, n_gpus=world_size, config=config,
        resume=resume, preload=preload, epochs=epochs,
        steps_per_epoch=steps_per_epoch, model=model,
        compute_metric=compute_metric, processor=processor,
        train_dl=train_dl, val_dl=val_dl, train_sampler=train_sampler,
        val_sampler=val_sampler, optimizer=optimizer, scheduler=scheduler,
        save_dir=save_dir, log_dir=log_dir,
        gradient_accumulation_steps=gradient_accumulation_steps,
        use_amp=use_amp, max_clip_grad_norm=max_clip_grad_norm
    )
    trainer.train()
    cleanup()
```

### A.4.2 Training Epoch Implementation

```python
def _train_epoch(self, epoch) -> None:
    """Single training epoch with mixed precision and gradient accumulation"""
    self.train_sampler.set_epoch(epoch)
    
    if self.rank == 0:
        print("Epoch {}: ".format(epoch+1))
        pbar = PBar(self.steps_per_epoch, 10, stateful_metrics=self.stateful_metrics)

    for dl_step, batch in enumerate(self.train_dl):
        with autocast(enabled=self.use_amp):
            # Forward pass
            self.model.train()
            outputs = self.model(**batch)
            
            # Loss computation with gradient accumulation
            loss = outputs.loss / self.gradient_accumulation_steps
        
        # Backward pass with gradient scaling
        self.scaler.scale(loss).backward()
        wer = torch.tensor(self.compute_metric(outputs.logits.detach(), batch['labels']))

        # Optimization step
        if (dl_step + 1) % self.gradient_accumulation_steps == 0 or dl_step == len(self.train_dl) - 1:
            # Gradient clipping
            grad_norm = self.get_grad_norm(self.model.parameters(), 
                                         scale=self.scaler.get_scale())
            self.scaler.unscale_(self.optimizer)
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 
                                         self.max_clip_grad_norm)

            # Parameter update
            scale_before = self.scaler.get_scale()
            self.scaler.step(self.optimizer)
            self.scaler.update()
            self.optimizer.zero_grad()
            
            scale_after = self.scaler.get_scale()
            is_overflown = scale_after < scale_before
            if not is_overflown:
                self.scheduler.step()
            
            # Logging and validation
            if self.n_gpus > 1:
                loss = self.gather(loss).mean()
                wer = self.gather(wer).mean()

            train_logs = {
                "loss": loss * self.gradient_accumulation_steps,
                "lr": self.optimizer.param_groups[0]['lr'],
                "grad_norm": grad_norm,
                "wer": wer
            }
            
            if self.rank == 0:
                self.writer.update(self.completed_steps, 'Train', train_logs)
                pbar.update(self.pbar_step+1, "train_", train_logs)
                
            # Validation
            if (self.completed_steps+1) % self.validation_interval == 0:
                if self.rank == 0:
                    print("\nValidation is in progress...")
                self.model.eval()
                val_logs = self._valid_epoch(self.completed_steps)
                
                if self.rank == 0:
                    self.writer.update(self.completed_steps, 'Validation', val_logs)
                    pbar.update(self.pbar_step+1, "val_", val_logs)

                    # Save best model
                    if self._is_best_epoch(val_logs['wer'], 
                                         save_max_metric_score=self.save_max_metric_score):
                        self._save_checkpoint(epoch, dl_step, is_best_epoch=True)
                    else:
                        self._save_checkpoint(epoch, dl_step, is_best_epoch=False)
                self.dist.barrier()
            
            self.pbar_step += 1
            self.completed_steps += 1

    self.pbar_step = 0
```

## A.5 Evaluation and Metrics

### A.5.1 Metric Computation

```python
class Metric:
    def __init__(self, processor):
        self.processor = processor
        self.wer_metric = load_metric("wer", trust_remote_code=True)

    def __call__(self, logits, labels):
        """Compute Word Error Rate (WER)"""
        preds = torch.argmax(logits, axis=-1)
        
        # Handle padding tokens
        labels[labels == -100] = self.processor.tokenizer.pad_token_id
        
        # Decode predictions and labels
        pred_strs = self.processor.batch_decode(preds, skip_special_tokens=True)
        label_strs = self.processor.batch_decode(labels, skip_special_tokens=True)
        
        # Compute WER
        wer = self.wer_metric.compute(predictions=pred_strs, references=label_strs)
        return wer
```

### A.5.2 Validation Implementation

```python
def _valid_epoch(self, step) -> Dict[str, Union[Any, float]]:
    """Validation epoch implementation"""
    self.val_sampler.set_epoch(step)
    
    val_logs = {
        "loss": 0,
        "wer": 0
    }

    for batch in tqdm(self.val_dl, total=len(self.val_dl), disable=not self.rank == 0):
        with torch.no_grad():
            with autocast(enabled=self.use_amp):
                outputs = self.model(**batch)

        val_logs["loss"] += outputs.loss / len(self.val_dl)
        val_logs["wer"] += torch.tensor(self.compute_metric(outputs.logits, 
                                                          batch['labels'])) / len(self.val_dl)

    # Average over devices in DDP
    if self.n_gpus > 1:
        val_logs = {k: self.gather(v).mean() for k, v in val_logs.items()}
    
    val_logs = {k: v.item() if hasattr(v, 'item') else v for k, v in val_logs.items()}
    return val_logs
```

## A.6 Inference Implementation

### A.6.1 Model Loading and Inference

```python
class Inferencer:
    def __init__(self, device, huggingface_folder, model_path, repo_name):
        self.device = device
        self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
        self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(device)
        
        if model_path is not None:
            self.preload_model(model_path)

    def preload_model(self, model_path):
        """Load custom model weights"""
        assert os.path.exists(model_path), f"The file {model_path} does not exist."
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint["model"], strict=True)
        print(f"Model preloaded successfully from {model_path}.")

    def transcribe(self, wav) -> str:
        """Transcribe audio to text"""
        input_values = self.processor(wav, sampling_rate=16000, 
                                    return_tensors="pt").input_values
        logits = self.model(input_values.to(self.device)).logits
        pred_ids = torch.argmax(logits, dim=-1)
        pred_transcript = self.processor.batch_decode(pred_ids)[0]
        return pred_transcript

    def run(self, test_filepath):
        """Run inference on audio file"""
        wav, _ = librosa.load(test_filepath, sr=16000)
        print(f"Transcript: {self.transcribe(wav)}")
```

### A.6.2 Batch Processing

```python
def batch_inference(self, test_filepath):
    """Batch inference for multiple audio files"""
    if test_filepath.endswith('.txt'):
        # Process list of files
        with open(test_filepath, 'r') as f:
            file_paths = [line.strip() for line in f.readlines()]
        
        results = []
        for file_path in tqdm(file_paths):
            wav, _ = librosa.load(file_path, sr=16000)
            transcript = self.transcribe(wav)
            results.append(f"{file_path}\t{transcript}")
        
        # Save results
        output_path = test_filepath.replace('.txt', '_transcript.txt')
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(results))
    else:
        # Single file processing
        self.run(test_filepath)
```

## A.7 Data Collation and Batching

### A.7.1 Custom Collate Function

```python
class DefaultCollate:
    def __init__(self, processor, sr):
        self.processor = processor
        self.sr = sr
        
    def __call__(self, inputs) -> Dict[str, torch.tensor]:
        """Collate function for batching audio and text data"""
        features, transcripts = zip(*inputs)
        features, transcripts = list(features), list(transcripts)
        
        # Process audio features
        batch = self.processor(features, sampling_rate=16000, 
                              padding="longest", return_tensors="pt")

        # Process text labels
        with self.processor.as_target_processor():
            labels_batch = self.processor(transcripts, padding="longest", 
                                        return_tensors="pt")

        # Mask padding tokens in labels
        batch["labels"] = labels_batch["input_ids"].masked_fill(
            labels_batch.attention_mask.ne(1), -100)

        return batch
```

## A.8 Memory Management and Optimization

### A.8.1 Gradient Norm Computation

```python
def get_grad_norm(self, params, scale=1) -> torch.tensor:
    """Compute gradient norm for monitoring"""
    total_norm = 0.0
    for p in params:
        if p.grad is not None:
            param_norm = (p.grad.detach().data / scale).norm(2)
            total_norm += param_norm.item() ** 2
    total_norm = total_norm**0.5
    return total_norm
```

### A.8.2 Distributed Data Gathering

```python
def gather(self, value: torch.tensor) -> Any:
    """Gather values across distributed devices"""
    if value.ndim == 0:
        value = value.clone()[None]
    output_tensors = [value.clone() for _ in range(self.dist.get_world_size())]
    self.dist.all_gather(output_tensors, value)
    return torch.cat(output_tensors, dim=0)
```

## A.9 Configuration Management

### A.9.1 Configuration Validation

```python
def validate_config(config):
    """Validate configuration parameters"""
    required_sections = ['meta', 'train_dataset', 'val_dataset', 'optimizer', 'scheduler', 'trainer']
    for section in required_sections:
        if section not in config:
            raise ValueError(f"Missing required configuration section: {section}")
    
    # Validate meta parameters
    meta = config['meta']
    required_meta = ['pretrained_path', 'epochs', 'sr', 'device_ids']
    for param in required_meta:
        if param not in meta:
            raise ValueError(f"Missing required meta parameter: {param}")
    
    # Validate dataset paths
    if not os.path.exists(config['train_dataset']['args']['path']):
        raise ValueError(f"Training dataset path does not exist: {config['train_dataset']['args']['path']}")
    
    if not os.path.exists(config['val_dataset']['args']['path']):
        raise ValueError(f"Validation dataset path does not exist: {config['val_dataset']['args']['path']}")
```

### A.9.2 Environment Setup

```python
def setup_environment(config):
    """Setup training environment"""
    # Set random seeds for reproducibility
    torch.manual_seed(config['meta']['seed'])
    np.random.seed(config['meta']['seed'])
    random.seed(config['meta']['seed'])
    
    # Set CUDA device
    if torch.cuda.is_available():
        torch.cuda.set_device(int(config['meta']['device_ids'].split(',')[0]))
    
    # Enable deterministic algorithms if needed
    if config.get('deterministic', False):
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
```

## A.10 Performance Monitoring

### A.10.1 Training Metrics Tracking

```python
class MetricsTracker:
    def __init__(self):
        self.metrics = {
            'train_loss': [],
            'train_wer': [],
            'val_loss': [],
            'val_wer': [],
            'learning_rate': [],
            'gradient_norm': []
        }
    
    def update(self, step, metric_type, values):
        """Update metrics for a training step"""
        for key, value in values.items():
            metric_key = f"{metric_type}_{key}"
            if metric_key in self.metrics:
                self.metrics[metric_key].append((step, value))
    
    def get_best_metric(self, metric_name):
        """Get best value for a specific metric"""
        if metric_name in self.metrics:
            values = [v for _, v in self.metrics[metric_name]]
            return min(values) if 'wer' in metric_name else max(values)
        return None
    
    def plot_metrics(self, save_path=None):
        """Plot training metrics"""
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot training loss
        if self.metrics['train_loss']:
            steps, values = zip(*self.metrics['train_loss'])
            axes[0, 0].plot(steps, values, label='Training Loss')
            axes[0, 0].set_title('Training Loss')
            axes[0, 0].legend()
        
        # Plot validation loss
        if self.metrics['val_loss']:
            steps, values = zip(*self.metrics['val_loss'])
            axes[0, 1].plot(steps, values, label='Validation Loss')
            axes[0, 1].set_title('Validation Loss')
            axes[0, 1].legend()
        
        # Plot WER
        if self.metrics['train_wer']:
            steps, values = zip(*self.metrics['train_wer'])
            axes[1, 0].plot(steps, values, label='Training WER')
        if self.metrics['val_wer']:
            steps, values = zip(*self.metrics['val_wer'])
            axes[1, 0].plot(steps, values, label='Validation WER')
        axes[1, 0].set_title('Word Error Rate')
        axes[1, 0].legend()
        
        # Plot learning rate
        if self.metrics['learning_rate']:
            steps, values = zip(*self.metrics['learning_rate'])
            axes[1, 1].plot(steps, values, label='Learning Rate')
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].legend()
        
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
        plt.show()
```

This technical appendix provides comprehensive implementation details for reproducing the ASR system architecture and training pipeline. All code examples are directly derived from the actual implementation and can be used for research reproduction and further development. 