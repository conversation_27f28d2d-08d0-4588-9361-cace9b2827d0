[meta]
name = "Quran-ASR" 
pretrained_path = "elgeish/wav2vec2-large-xlsr-53-arabic"
seed = 42
epochs = 15
save_dir = "E:/Quran Trained/10_full_quran/"
gradient_accumulation_steps = 2
use_amp = true # Whether to use Automatic Mixed Precision for speeding up - https://pytorch.org/docs/stable/amp.html
device_ids = "0" # set the gpu devices on which you want to train your model
sr = 16000
max_clip_grad_norm = 5.0 # torch.nn.utils.clip_grad_norm_

[special_tokens]
bos_token = "<bos>"
eos_token = "<eos>"
unk_token = "<unk>"
pad_token = "<pad>"


# Not available yet
[huggingface]
# You need to install git-lfs to be able to push
# Check out https://huggingface.co/docs/hub/how-to-upstream#repository to understand the parameters
push_to_hub = false
push_every_validation_step = false # If false, repo will be push at the end of training [recommended false]
overwrite_output_dir = false
blocking = false # whether to wait until the model is uploaded (this will be very slow because of large file) [recommended false, true only if push_every_validation_step is false]

    # you can pass your auth_token from your huggingface account to use_auth_token.
    # Otherwise you need to run ```huggingface-cli login``` command to log in
    [huggingface.args]
    local_dir = "E:/Quran Trained/10_full_quran/huggingface-hub" # where your repo places in local
    use_auth_token = true # you must provide the auth_token of your huggingface account. 
    clone_from = "" # path to your repo in huggingface



[train_dataset]
path = "base.base_dataset.BaseDataset"
    [train_dataset.args]
    path = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/10-train.txt"
    preload_data = false
    delimiter = "|"
    # Only train audio files that have duration in range [min_duration, max_duration]
    # min_duration = 0 # if not pass, default is -np.inf
    # max_duration = 30 # if not pass, default is np.inf
    nb_workers = 16
    
    [train_dataset.dataloader]
    batch_size = 2
    num_workers = 16
    pin_memory = true 
    drop_last = true

    [train_dataset.sampler]
    shuffle = true 
    drop_last = true


[val_dataset]
path = "base.base_dataset.BaseDataset"
    [val_dataset.args]
    path = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/10-test.txt"
    preload_data = false
    delimiter = "|"
    nb_workers = 16

    [val_dataset.dataloader]
    batch_size =  1 # Set validation batch_size > 1 may yield an incorrect score due to padding (but faster :D) - https://github.com/pytorch/fairseq/issues/3227 
    num_workers = 4

    [val_dataset.sampler]
    shuffle = false 
    drop_last = false

 
[optimizer]
lr = 1e-6


[scheduler] 
max_lr = 5e-4
    

[trainer]
path = "trainer.trainer.Trainer"
    [trainer.args]
    validation_interval = 5000
    save_max_metric_score = false 
