import os
import shutil
from pydub import AudioSegment

# Define the source directory and the destination directory
source_dir = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train - Copy"
dest_dir = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Parah-30-dataset"

# Create the destination directory if it doesn't exist
os.makedirs(dest_dir, exist_ok=True)

# Loop through each subfolder in the source directory
for subdir in os.listdir(source_dir):
    subdir_path = os.path.join(source_dir, subdir)

    if os.path.isdir(subdir_path):
        # Loop through each file in the subfolder
        for file in os.listdir(subdir_path):
            file_path = os.path.join(subdir_path, file)

            if os.path.isfile(file_path) and file.endswith(".mp3"):
                # Convert the MP3 file to WAV format
                wav_file_path = file_path[:-4] + ".wav"
                AudioSegment.from_mp3(file_path).export(wav_file_path, format="wav")

                # Create the new filename
                new_filename = f"{subdir}_{file[:-4]}.wav"
                new_file_path = os.path.join(dest_dir, new_filename)

                # Copy and rename the converted WAV file to the destination directory
                shutil.copy(wav_file_path, new_file_path)

                # Remove the original MP3 file and the temporary WAV file
                os.remove(file_path)
                os.remove(wav_file_path)

print("Files have been converted, merged, and renamed successfully.")



#Dataset Split

# import os
# import shutil
# import random

# # Define your dataset directory and text file path
# data_dir = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Parah-30-dataset"
# text_file_path = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/parah-30-dataset.txt"

# # Define the train and test directories
# train_dir = os.path.join(data_dir, "train")
# test_dir = os.path.join(data_dir, "test")

# # Create the train and test directories if they don't exist
# os.makedirs(train_dir, exist_ok=True)
# os.makedirs(test_dir, exist_ok=True)

# # Read the lines from the text file
# with open(text_file_path, "r", encoding='utf-8') as file:
#     lines = file.readlines()

# # Split the lines randomly into train and test
# random.shuffle(lines)
# split_index = int(len(lines) * 0.8)
# train_lines = lines[:split_index]
# test_lines = lines[split_index:]

# # Move the files to the train and test directories and write to the new text files
# with open(os.path.join(data_dir, "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/parah-30-dataset-train.txt"), "w", encoding='utf-8') as train_file, open(os.path.join(data_dir, "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/parah-30-dataset-test.txt"), "w", encoding='utf-8') as test_file:
#     for line in train_lines:
#         file_path, transcript = line.strip().split("|")
#         shutil.copy(file_path, train_dir)
#         train_file.write(f"{os.path.join(train_dir, os.path.basename(file_path))}|{transcript}\n")

#     for line in test_lines:
#         file_path, transcript = line.strip().split("|")
#         shutil.copy(file_path, test_dir)
#         test_file.write(f"{os.path.join(test_dir, os.path.basename(file_path))}|{transcript}\n")



# import os

# # Path to the text file containing Surah Shams
# surah_shams_path = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/Surah Shams.txt"

# # Base directory containing all the folders
# base_dir = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/test_dataset_wav'

# # Open the Surah Shams file
# with open(surah_shams_path, 'r', encoding='utf-8') as file:
#     lines = file.readlines()

# # Initialize an empty list to store the results
# results = []

# # Loop through all the folders in the base directory
# for folder in os.listdir(base_dir):
#     folder_path = os.path.join(base_dir, folder)
#     if os.path.isdir(folder_path):
#         # Loop through all the files in the current folder
#         for i, filename in enumerate(os.listdir(folder_path)):
#             if i >= len(lines):  # Stop iterating if there are no more lines to process
#                 break
#             file_path = os.path.join(folder_path, filename)
#             if os.path.isfile(file_path):
#                 # Append the path and the corresponding line from Surah Shams to the results list
#                 results.append(f'{file_path}|{lines[i].strip()}')

# # Write the results to a text file
# with open('D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/test.txt', 'w', encoding='utf-8') as file:
#     for result in results:
#         file.write(result + '\n')



# import soundfile as sf
# import pyloudnorm as pyln
# import numpy as np
# import librosa
# import librosa.display
# import matplotlib.pyplot as plt

# # Load the audio file
# data, rate = sf.read("D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/test_dataset_wav/9/091009.wav")

# # Create BS.1770 meter
# meter = pyln.Meter(rate)
# loudness = meter.integrated_loudness(data)
# print(f"Original loudness: {loudness} LUFS")

# # Peak normalize audio to max_avg
# max_avg = 0.5518409609794617
# peak_normalized_audio = pyln.normalize.peak(data, max_avg)
# peak_normalized_audio = np.clip(peak_normalized_audio, -1.0, 1.0)  # Clip to avoid possible clipped samples

# # Loudness normalize audio to -12 dB LUFS
# loudness_normalized_audio = pyln.normalize.loudness(data, loudness, -12.0)
# loudness_normalized_audio = np.clip(loudness_normalized_audio, -1.0, 1.0)  # Clip to avoid possible clipped samples

# # Min-Max normalization to min_avg and max_avg
# min_avg = -0.506213903427124
# normalized_min = np.min(data)
# normalized_max = np.max(data)

# min_max_normalized_audio = (data - normalized_min) / (normalized_max - normalized_min)  # Scale to 0 - 1
# min_max_normalized_audio = min_max_normalized_audio * (max_avg - min_avg) + min_avg    # Scale to min_avg - max_avg
# min_max_normalized_audio = np.clip(min_max_normalized_audio, -1.0, 1.0)  # Clip to avoid possible clipped samples

# # Save normalized audios
# sf.write("peak_normalized_audio.wav", peak_normalized_audio, rate)
# sf.write("loudness_normalized_audio.wav", loudness_normalized_audio, rate)
# sf.write("min_max_normalized_audio.wav", min_max_normalized_audio, rate)

# # Function to plot Mel spectrogram
# # def plot_mel_spectrogram(audio, rate, title):
# #     n_fft = min(2048, len(audio) - 1)  # Ensure n_fft is appropriate for the signal length
# #     hop_length = n_fft // 4
    
# #     mel_spectrogram = librosa.feature.melspectrogram(y=audio, sr=rate, n_fft=n_fft, hop_length=hop_length, n_mels=128)
# #     mel_spectrogram_db = librosa.power_to_db(mel_spectrogram, ref=np.max)
    
# #     plt.figure(figsize=(10, 4))
# #     librosa.display.specshow(mel_spectrogram_db, sr=rate, hop_length=hop_length, x_axis='time', y_axis='mel')
# #     plt.colorbar(format='%+2.0f dB')
# #     plt.title(title)
# #     plt.tight_layout()
# #     plt.show()

# # # Plot Mel spectrograms
# # plot_mel_spectrogram(peak_normalized_audio, rate, 'Peak Normalized Audio')
# # plot_mel_spectrogram(loudness_normalized_audio, rate, 'Loudness Normalized Audio')
# # plot_mel_spectrogram(min_max_normalized_audio, rate, 'Min-Max Normalized Audio')

# # Print normalization results
# new_loudness = meter.integrated_loudness(loudness_normalized_audio)
# print(f"Loudness normalized to: {new_loudness} LUFS")

# print(f"Peak normalized audio min: {np.min(peak_normalized_audio)}, max: {np.max(peak_normalized_audio)}")
# print(f"Loudness normalized audio min: {np.min(loudness_normalized_audio)}, max: {np.max(loudness_normalized_audio)}")
# print(f"Min-max normalized audio min: {np.min(min_max_normalized_audio)}, max: {np.max(min_max_normalized_audio)}")


# import librosa
# import numpy as np
# import scipy.io.wavfile as wavfile

# # Load the audio file
# audio, sr = librosa.load("D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/test_dataset_wav/9/091009.wav")

# # Calculate the maximum and minimum absolute values of the audio signal
# max_abs_value = np.max(np.abs(audio))
# min_abs_value = np.min(np.abs(audio))

# # Define the target maximum and minimum values
# target_max = 1.0
# target_min = -1.0

# # Calculate the normalization factor
# norm_factor = (target_max - target_min) / (max_abs_value - min_abs_value)

# # Normalize the audio
# normalized_audio = (audio - min_abs_value) * norm_factor + target_min

# # Save the normalized audio
# wavfile.write('normalized_audio.wav', sr, normalized_audio.astype(np.int16))
