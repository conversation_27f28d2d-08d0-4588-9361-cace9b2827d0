import os
import sys
import shutil
from pathlib import Path
from torch.multiprocessing import Pool, set_start_method
from tqdm import tqdm
import torchaudio
# Configuration
NUM_GPUS = 16  # Adjust based on your GPU count
TARGET_SR = 16000  # 16 kHz sample rate
def find_mp3_files(main_folder):
    """Find all MP3 files in subfolders of the main folder."""
    mp3_files = []
    for root, _, files in os.walk(main_folder):
        for file in files:
            if file.lower().endswith('.mp3'):
                mp3_files.append(Path(root) / file)
    return mp3_files
def convert_mp3_to_wav(args):
    """Convert a single MP3 file to WAV using GPU."""
    mp3_path, gpu_id = args
    try:
        # Load MP3
        waveform, sample_rate = torchaudio.load(mp3_path)
        # Resample to 16 kHz if needed
        if sample_rate != TARGET_SR:
            resampler = torchaudio.transforms.Resample(sample_rate, TARGET_SR)
            waveform = resampler(waveform)
        # Save as WAV in the same subfolder
        wav_path = mp3_path.with_suffix('.wav')
        torchaudio.save(wav_path, waveform, TARGET_SR)
        return str(mp3_path), True
    except Exception as e:
        print(f"Error converting {mp3_path}: {e}")
        return str(mp3_path), False
def process_files_parallel(mp3_files):
    """Process files in parallel using all GPUs."""
    set_start_method('spawn', force=True)
    files_with_gpu = [(file, i % NUM_GPUS) for i, file in enumerate(mp3_files)]
    success_count = 0
    with tqdm(total=len(mp3_files), desc="Converting MP3 to WAV", unit="file") as pbar:
        with Pool(processes=NUM_GPUS) as pool:
            for _, success in pool.imap_unordered(convert_mp3_to_wav, files_with_gpu):
                if success:
                    success_count += 1
                pbar.update(1)
                pbar.set_description(f"Converted ({success_count}/{len(mp3_files)})")
    print(f"\nDone! Successfully converted {success_count}/{len(mp3_files)} files.")
    return success_count
def create_identical_folder_structure(original_root, new_root):
    """Create an identical folder structure in the new location."""
    for root, dirs, _ in os.walk(original_root):
        for dir_name in dirs:
            original_dir = Path(root) / dir_name
            relative_path = original_dir.relative_to(original_root)
            new_dir = new_root / relative_path
            new_dir.mkdir(parents=True, exist_ok=True)
def move_mp3_files(original_root, new_root):
    """Move all MP3 files to the new location, preserving folder structure."""
    mp3_files = []
    for root, _, files in os.walk(original_root):
        for file in files:
            if file.lower().endswith('.mp3'):
                mp3_files.append(Path(root) / file)
    for mp3_file in tqdm(mp3_files, desc="Moving MP3 files", unit="file"):
        relative_path = mp3_file.relative_to(original_root)
        new_mp3_path = new_root / relative_path
        new_mp3_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.move(str(mp3_file), str(new_mp3_path))
def main():
    main_folder = Path("/nvme0n1-disk/ASR_Training/audio_data")
    mp3_destination_folder = Path("/nvme0n1-disk/ASR_Training/audio_data_mp3")
    if not main_folder.exists() or not main_folder.is_dir():
        print(f"Error: Directory '{main_folder}' is invalid!")
        sys.exit(1)
    mp3_files = find_mp3_files(main_folder)
    if not mp3_files:
        print("No MP3 files found in any subfolder!")
        sys.exit(1)
    print(f"Found {len(mp3_files)} MP3 files in subfolders. Converting to WAV at {TARGET_SR} Hz...")
    success_count = process_files_parallel(mp3_files)
    print(f"Creating folder structure in '{mp3_destination_folder}'...")
    create_identical_folder_structure(main_folder, mp3_destination_folder)
    print(f"Moving MP3 files from '{main_folder}' to '{mp3_destination_folder}'...")
    move_mp3_files(main_folder, mp3_destination_folder)
    print("Done! All MP3 files have been converted to WAV and moved.")
if __name__ == "__main__":
    main()