import io
from fastapi import FastAPI, UploadFile
from io import BytesIO
import librosa
import torch
import os
import torchaudio
from transformers import Wav2Vec2Processor, Wav2Vec2ForCTC
import pickle
import torch.nn.functional as F
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification

app = FastAPI()
device = "cuda" if torch.cuda.is_available() else "cpu"

class Inferencer_Parah_30:
    def __init__(self, device, huggingface_folder, model_path=None):
        self.device = device
        self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
        self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(self.device)
        if model_path is not None:
            self.preload_model(model_path)

    def preload_model(self, model_path):
        assert os.path.exists(model_path), f"The file {model_path} does not exist. Please check the path."
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint["model"], strict=True)
        print(f"Model preloaded successfully from {model_path}.")

    def transcribe(self, wav):
        input_values = self.processor(wav, sampling_rate=16000, return_tensors="pt").input_values
        logits = self.model(input_values.to(self.device)).logits
        pred_ids = torch.argmax(logits, dim=-1)
        pred_transcript = self.processor.batch_decode(pred_ids)[0]
        return pred_transcript

inferencer_parah_30 = Inferencer_Parah_30(
    device="cuda" if torch.cuda.is_available() else "cpu",
    huggingface_folder="huggingface-Parah 30",
    model_path="Parah 30/Quran-ASR/checkpoints/best_model.tar")


# class Inferencer_Al_Shams:
#     def __init__(self, device, huggingface_folder, model_path=None):
#         self.device = device
#         self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
#         self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(self.device)
#         if model_path is not None:
#             self.preload_model(model_path)

#     def preload_model(self, model_path):
#         assert os.path.exists(model_path), f"The file {model_path} does not exist. Please check the path."
#         checkpoint = torch.load(model_path, map_location=self.device)
#         self.model.load_state_dict(checkpoint["model"], strict=True)
#         print(f"Model preloaded successfully from {model_path}.")

#     def transcribe(self, wav):
#         input_values = self.processor(wav, sampling_rate=16000, return_tensors="pt").input_values
#         logits = self.model(input_values.to(self.device)).logits
#         pred_ids = torch.argmax(logits, dim=-1)
#         pred_transcript = self.processor.batch_decode(pred_ids)[0]
#         return pred_transcript

# inferencer_Al_Shams = Inferencer_Al_Shams(
#     device="cuda" if torch.cuda.is_available() else "cpu",
#     huggingface_folder="huggingface-Shams",
#     model_path="Al-Shams/Quran-ASR/checkpoints/best_model.tar")

# def load_model_and_extractor_lesson1():
#     # Load the saved label2id mapping
    

#     return model, feature_extractor, id2label

id2label_lesson1 = "D:/Quran Learning/audio-classification-pytorch/results/best/label2id.pkl"
with open(id2label_lesson1, 'rb') as f:
    id2label_lesson1 = pickle.load(f)
id2label_lesson1 = {v: k for k, v in id2label_lesson1.items()}
# Load the saved model
model_lesson1_path = "D:/Quran Learning/audio-classification-pytorch/results/best"
model_lesson1 = AutoModelForAudioClassification.from_pretrained(model_lesson1_path, num_labels=len(id2label_lesson1))
model_lesson1 = model_lesson1.to(device)
# Load the feature extractor
feature_extractor_lesson1 = AutoFeatureExtractor.from_pretrained("facebook/wav2vec2-base")

# def load_model_and_extractor_lesson3():
#     # Load the saved label2id mapping
    

#     return model, feature_extractor, id2label

id2label_lesson3_path = "D:/Quran Learning/audio-classification-pytorch/Lesson3halfresults/best/label2id.pkl"
with open(id2label_lesson3_path, 'rb') as f:
    id2label_lesson3 = pickle.load(f)
id2label_lesson3 = {v: k for k, v in id2label_lesson3.items()}
# Load the saved model
model_lesson3_path = "D:/Quran Learning/audio-classification-pytorch/Lesson3halfresults/best"
model_lesson3 = AutoModelForAudioClassification.from_pretrained(model_lesson3_path, num_labels=len(id2label_lesson3))
model_lesson3 = model_lesson3.to(device)
# Load the feature extractor
feature_extractor_lesson3 = AutoFeatureExtractor.from_pretrained("facebook/wav2vec2-base")


@app.post("/qaida-lesson-3")
async def predict(file: UploadFile):
    print(file.content_type)
    # if file.content_type != "audio/mp3":
    #     return {"error": "File must be an MP3 file"}

    try:
        audio_data = await file.read()
        audio = io.BytesIO(audio_data)
        waveform, sr = librosa.load(audio)
        waveform = torch.from_numpy(waveform).unsqueeze(0)
        waveform = torchaudio.transforms.Resample(sr, 16_000)(waveform)
        inputs = feature_extractor_lesson3(waveform, sampling_rate=feature_extractor_lesson3.sampling_rate,
                                    max_length=16000, truncation=True)
        tensor = torch.tensor(inputs['input_values'][0]).to(device)

        with torch.no_grad():
            output = model_lesson3(tensor)
            logits = output['logits'][0]
            probabilities = F.softmax(logits, dim=0)  # Apply softmax to get probabilities
            _, top_indices = torch.topk(probabilities, k=5)  # Get the top 3 indices
            top_probabilities = probabilities[top_indices]  # Get the top 3 probabilities
            top_labels = [id2label_lesson3[str(idx.item())] for idx in top_indices]  # Get the top 3 labels

        result = [{"probability": float(prob), "label": label} for prob, label in zip(top_probabilities, top_labels)]
        print(result)
        return result
    except Exception as e:
        print(e)
        return e

@app.post("/qaida-lesson-1")
async def predict(file: UploadFile):
    print(file.content_type)
    # if file.content_type != "audio/mp3":
    #     return {"error": "File must be an MP3 file"}

    try:
        audio_data = await file.read()
        audio = io.BytesIO(audio_data)
        waveform, sr = librosa.load(audio)
        waveform = torch.from_numpy(waveform).unsqueeze(0)
        waveform = torchaudio.transforms.Resample(sr, 16_000)(waveform)
        inputs = feature_extractor_lesson1(waveform, sampling_rate=feature_extractor_lesson1.sampling_rate,
                                    max_length=16000, truncation=True)
        tensor = torch.tensor(inputs['input_values'][0]).to(device)

        with torch.no_grad():
            output = model_lesson1(tensor)
            logits = output['logits'][0]
            probabilities = F.softmax(logits, dim=0)  # Apply softmax to get probabilities
            _, top_indices = torch.topk(probabilities, k=5)  # Get the top 3 indices
            top_probabilities = probabilities[top_indices]  # Get the top 3 probabilities
            top_labels = [id2label_lesson1[str(idx.item())] for idx in top_indices]  # Get the top 3 labels

        result = [{"probability": float(prob), "label": label} for prob, label in zip(top_probabilities, top_labels)]
        print(result)
        return result
    except Exception as e:
        print(e)
        return e

@app.post("/transcription-parah-30/")
async def create_transcription_al_fatihah(file: UploadFile):
    try:
        wav, _ = librosa.load(BytesIO(await file.read()), sr=16000)
        transcript = inferencer_parah_30.transcribe(wav)
        return {"transcript": transcript}
    except Exception as e:
        return {"error": str(e)}
    
# @app.post("/transcription-al-Shams/")
# async def create_transcription_al_shams(file: UploadFile):
#     try:
#         wav, _ = librosa.load(BytesIO(await file.read()), sr=16000)
#         transcript = inferencer_Al_Shams.transcribe(wav)
#         return {"transcript": transcript}
#     except Exception as e:
#         return {"error": str(e)}


# @app.post("/qaida-lesson-1/")
# async def create_qaida_lesson_1_transcription(file: UploadFile):
#     try:
#         wav, _ = librosa.load(BytesIO(await file.read()), sr=None)
#         print(wav)
#         prediction = predict_lesson1(wav)
#         return prediction
#     except Exception as e:
#         return {"error": str(e)}

# @app.post("/qaida-lesson-3/")
# async def create_qaida_lesson_3_transcription(file: UploadFile):
#     try:
#         wav, _ = librosa.load(BytesIO(await file.read()), sr=None)
#         prediction = predict_lesson3(wav)
#         return prediction
#     except Exception as e:
#         return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="*************", port=8000)








# from fastapi import FastAPI, File, UploadFile
# from io import BytesIO
# import librosa
# import torch
# import os
# from transformers import Wav2Vec2Processor, Wav2Vec2ForCTC

# app = FastAPI()

# class Inferencer:
#     def __init__(self, device, huggingface_folder, model_path=None):
#         self.device = device
#         self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
#         self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(self.device)
#         if model_path is not None:
#             self.preload_model(model_path)

#     def preload_model(self, model_path):
#         assert os.path.exists(model_path), f"The file {model_path} does not exist. Please check the path."
#         checkpoint = torch.load(model_path, map_location=self.device)
#         self.model.load_state_dict(checkpoint["model"], strict=True)
#         print(f"Model preloaded successfully from {model_path}.")

#     def transcribe(self, wav):
#         input_values = self.processor(wav, sampling_rate=16000, return_tensors="pt").input_values
#         logits = self.model(input_values.to(self.device)).logits
#         pred_ids = torch.argmax(logits, dim=-1)
#         pred_transcript = self.processor.batch_decode(pred_ids)[0]
#         return pred_transcript

# inferencer = Inferencer(
#     device="cuda" if torch.cuda.is_available() else "cpu",
#     huggingface_folder="huggingface-hub",
#     model_path="Al-Fatihah/Quran-ASR/checkpoints/best_model.tar")

# @app.post("/transcription/")
# async def create_transcription(file: UploadFile):
#     try:
#         wav, _ = librosa.load(BytesIO(await file.read()), sr=16000)
#         transcript = inferencer.transcribe(wav)
#         return {"transcript": transcript}
#     except Exception as e:
#         return {"error": str(e)}

# if __name__ == "__main__":
#     import uvicorn

#     uvicorn.run(app, host="*************", port=8000)
