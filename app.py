from flask import Flask, request, jsonify
import time
import google.generativeai as genai
import librosa
import io
import soundfile as sf
import torch  # Import PyTorc
app = Flask(__name__)
# Replace with your Gemini API key
GOOGLE_API_KEY = "AIzaSyCXvMfsrSkI6vXjTDZtyCI9lRqwvAAI0bg"
genai.configure(api_key=GOOGLE_API_KEY)
# Select the Gemini model
model = genai.GenerativeModel("gemini-2.0-flash-lite")
def audio_to_bytes(audio_file):
    """Loads an audio file and converts it to a bytes object suitable for Gemini."""
    try:
        # Load audio using librosa
        audio_array, sr = librosa.load(audio_file, sr=16000, dtype='float32')
        # Convert the audio array to a PyTorch tensor and move it to the GPU
        audio_tensor = torch.tensor(audio_array).cuda()
        # Convert the tensor back to a NumPy array for soundfile
        audio_array_np = audio_tensor.cpu().numpy()
        # Convert the audio array to a bytes object
        audio_bytes_io = io.BytesIO()
        sf.write(audio_bytes_io, audio_array_np, sr, format='WAV')  # Use soundfile
        audio_bytes = audio_bytes_io.getvalue()
        return audio_bytes
    except Exception as e:
        print(f"Error converting audio to bytes: {e}")
        return None
@app.route('/transcribe', methods=['POST'])
def transcribe_audio():
    if 'audio' not in request.files:
        return jsonify({"error": "No audio file provided"}), 400
    audio_file = request.files['audio']
    audio_bytes = audio_to_bytes(audio_file)
    if audio_bytes:
        # Construct the Gemini content with the audio data
        contents = [
            {
                "mime_type": "audio/wav",
                "data": audio_bytes
            },
            "Diacritize Arabic audio: ONLY tashkeel." # Modified prompt
        ]
        start_time = time.time()
        try:
            response = model.generate_content(contents)
            end_time = time.time()
            response_time = end_time - start_time
            # Extract only the transcription text, removing any leading/trailing whitespace.
            transcription = response.text.strip()
            return jsonify({
                "transcription": transcription,
                "response_time": response_time
            })
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    else:
        return jsonify({"error": "Failed to convert audio to bytes"}), 500
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000)