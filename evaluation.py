# import torch
# import os
# from transformers import Wav2Vec2Processor, Wav2Vec2ForCTC
# import librosa
# import jiwer
# from tqdm import tqdm
# from huggingface_hub import HfA<PERSON>, HfFolder

# class Evaluator:
#     def __init__(self, device, huggingface_folder, model_path, manifest_filepath, repo_id) -> None:
#         self.device = device
#         self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
#         self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(self.device)
#         if model_path is not None:
#             self.preload_model(model_path)
#         self.manifest_filepath = manifest_filepath
#         self.repo_id = repo_id

#     def preload_model(self, model_path) -> None:
#         assert os.path.exists(model_path), f"The file {model_path} does not exist. Please check the path."
#         checkpoint = torch.load(model_path, map_location=self.device)
#         self.model.load_state_dict(checkpoint["model"], strict=True)
#         print(f"Model preloaded successfully from {model_path}.")

#     def transcribe(self, wav) -> str:
#         input_values = self.processor(wav, sampling_rate=16000, return_tensors="pt").input_values
#         logits = self.model(input_values.to(self.device)).logits
#         pred_ids = torch.argmax(logits, dim=-1)
#         pred_transcript = self.processor.batch_decode(pred_ids)[0]
#         return pred_transcript

#     def evaluate(self):
#         cer = 0.0
#         wer = 0.0
#         num_samples = 0
#         with open(self.manifest_filepath, "r", encoding='utf-8') as f:
#             lines = f.readlines()
#         for line in tqdm(lines):
#             audio_filepath, transcript = line.strip().split("|")
#             wav, _ = librosa.load(audio_filepath, sr=16000)
#             pred_transcript = self.transcribe(wav)
#             cer += jiwer.cer(transcript, pred_transcript)
#             wer += jiwer.wer(transcript, pred_transcript)
#             num_samples += 1
#         cer /= num_samples
#         wer /= num_samples

#         print(f"Character Error Rate (CER): {cer:.4f}")
#         print(f"Word Error Rate (WER): {wer:.4f}")

#     def push_to_hub(self):
#         self.processor.push_to_hub(self.repo_id)
#         self.model.push_to_hub(self.repo_id)
#         print(f"Model and processor pushed to the Hugging Face Hub at {self.repo_id}")

# # Directly pass the parameters
# device = "cuda" if torch.cuda.is_available() else "cpu"
# huggingface_folder = "E:/Quran Trained/10_full_quran/huggingface-hub"
# model_path = "E:/Quran Trained/10_full_quran/Quran-ASR/checkpoints/best_model.tar"
# manifest_filepath = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/test - Copy.txt"
# repo_id = "your-username/your-model-repo"  # Replace with your Hugging Face username and model repo

# evaluator = Evaluator(
#     device=device,
#     huggingface_folder=huggingface_folder,
#     model_path=model_path,
#     manifest_filepath=manifest_filepath,
#     repo_id=repo_id
# )

# # Evaluate the model
# evaluator.evaluate()

# # Push the model to the Hugging Face Hub
# evaluator.push_to_hub()




import torch
import os
from transformers import Wav2Vec2Processor, Wav2Vec2ForCTC
import librosa
import jiwer
from tqdm import tqdm
from huggingface_hub import HfApi, HfFolder, login

login("*************************************")

class Evaluator:
    def __init__(self, device, model_repo_id, manifest_filepath, repo_id) -> None:
        self.device = device
        self.processor = Wav2Vec2Processor.from_pretrained(model_repo_id)
        self.model = Wav2Vec2ForCTC.from_pretrained(model_repo_id).to(self.device)
        self.manifest_filepath = manifest_filepath
        self.repo_id = repo_id

    def transcribe(self, wav) -> str:
        input_values = self.processor(wav, sampling_rate=16000, return_tensors="pt").input_values
        logits = self.model(input_values.to(self.device)).logits
        pred_ids = torch.argmax(logits, dim=-1)
        pred_transcript = self.processor.batch_decode(pred_ids)[0]
        return pred_transcript

    def evaluate(self):
        cer = 0.0
        wer = 0.0
        num_samples = 0
        with open(self.manifest_filepath, "r", encoding='utf-8') as f:
            lines = f.readlines()
        for line in tqdm(lines):
            audio_filepath, transcript = line.strip().split("|")
            wav, _ = librosa.load(audio_filepath, sr=16000)
            pred_transcript = self.transcribe(wav)
            cer += jiwer.cer(transcript, pred_transcript)
            wer += jiwer.wer(transcript, pred_transcript)
            num_samples += 1
        cer /= num_samples
        wer /= num_samples

        print(f"Character Error Rate (CER): {cer:.4f}")
        print(f"Word Error Rate (WER): {wer:.4f}")

    def push_to_hub(self):
        self.processor.push_to_hub(self.repo_id)
        self.model.push_to_hub(self.repo_id)
        print(f"Model and processor pushed to the Hugging Face Hub at {self.repo_id}")

# Directly pass the parameters
device = "cuda" if torch.cuda.is_available() else "cpu"
model_repo_id = "haris-waqar/Quran-ASR-Full-v2"  # Replace with the Hugging Face model repo ID
manifest_filepath = "100_ayats/100_ayats.txt"
repo_id = "your-username/your-model-repo"  # Replace with your Hugging Face username and model repo

evaluator = Evaluator(
    device=device,
    model_repo_id=model_repo_id,
    manifest_filepath=manifest_filepath,
    repo_id=repo_id
)

# Evaluate the model
evaluator.evaluate()

# Push the model to the Hugging Face Hub
# evaluator.push_to_hub()
