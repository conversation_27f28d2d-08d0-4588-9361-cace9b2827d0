import librosa
import torch
import os
from transformers import Wav2Vec2Processor, Wav2Vec2ForCTC
from tqdm import tqdm
from huggingface_hub import login

login("*************************************")

class Inferencer:
    def __init__(self, device, huggingface_folder, model_path, repo_name) -> None:
        self.device = device
        self.processor = Wav2Vec2Processor.from_pretrained(huggingface_folder)
        self.processor.push_to_hub(repo_name)
        self.model = Wav2Vec2ForCTC.from_pretrained(huggingface_folder).to(self.device)
        self.model.push_to_hub(repo_name)
        if model_path is not None:
            self.preload_model(model_path)

    def preload_model(self, model_path) -> None:
        assert os.path.exists(model_path), f"The file {model_path} is not exist. please check path."
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint["model"], strict = True)
        self.model.push_to_hub(repo_name)
        print(f"Model preloaded successfully from {model_path}.")

    def transcribe(self, wav) -> str:
        input_values = self.processor(wav, sampling_rate=16000, return_tensors="pt").input_values
        logits = self.model(input_values.to(self.device)).logits
        pred_ids = torch.argmax(logits, dim=-1)
        pred_transcript = self.processor.batch_decode(pred_ids)[0]
        return pred_transcript

    def run(self, test_filepath):
        wav, _ = librosa.load(test_filepath, sr = 16000)
        print(f"Transcript: {self.transcribe(wav)}")

# Directly pass the parameters
device = "cuda" if torch.cuda.is_available() else "cpu"
repo_name = "Quran-ASR-Full"
huggingface_folder = "E:/Quran Trained/10_full_quran/huggingface-hub"
model_path = "E:/Quran Trained/10_full_quran/Quran-ASR/checkpoints/best_model.tar"
test_filepath = "C:/Users/<USER>/Documents/Sound Recordings/Recording.wav" #"D:\Quran Learning\Quran Speech Dataset\archive\Quran_Ayat_public\Quran Data\destination_directory\test_wav\Husary_64kbps\002005.wav"

inferencer = Inferencer(
    repo_name=repo_name,
    device = device,
    huggingface_folder = huggingface_folder,
    model_path = model_path)

inferencer.run(test_filepath)