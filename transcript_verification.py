# Open the file in read mode
with open("D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/dataset/test.txt", 'r', encoding='utf-8') as file:
    # Read all lines from the file
    lines = file.readlines()

# Initialize a counter for empty transcripts
empty_transcripts_count = 0

# Iterate over each line in the file
for line in lines:
    # Split the line into path and transcript
    path, transcript = line.strip().split('|')
    # Check if the transcript is empty
    if not transcript.strip():
        # Increment the counter if the transcript is empty
        empty_transcripts_count += 1

# Print the number of empty transcripts found
print(f'Number of test empty transcripts: {empty_transcripts_count}')
