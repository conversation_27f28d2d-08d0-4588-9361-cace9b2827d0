# ----------------------Transcriptions Extraction---------------------->


# def extract_lines(input_file, output_file, start_num, end_num):
#     with open(input_file, 'r', encoding='utf-8') as infile, open(output_file, 'w', encoding='utf-8') as outfile:
#         for line in infile:
#             # Extract the number part before .mp3
#             parts = line.split('/')[-1].split('.')[0]
#             try:
#                 number = int(parts)
#                 if start_num <= number <= end_num:
#                     outfile.write(line)
#             except ValueError:
#                 # Skip lines that do not contain a valid number
#                 continue

# # Define the input and output file paths
# input_file = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/test.txt"
# output_file = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/third_10_test.txt"

# # Define the range of numbers to extract
# start_num = 29045
# end_num = 114006

# # Call the function to extract the lines
# extract_lines(input_file, output_file, start_num, end_num)



# ----------------------Audio Files moving---------------------->


# import os
# import shutil

# # Define the path to the text file containing the audio file paths
# text_file_path = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/third_10_test.txt"

# # Define the new directory where you want to move the audio files
# new_directory = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/third_10_test'

# # Define the original base path
# original_base_path = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/'

# # Read the text file and extract the file paths
# with open(text_file_path, 'r', encoding='utf-8') as file:
#     lines = file.readlines()

# # Iterate through each line and move the audio files
# for line in lines:
#     # Split the line to get the file path
#     file_path = line.split('|')[0].strip()

#     # Check if the file exists
#     # if not os.path.exists(file_path):
#     #     print(f"File not found: {file_path}")
#     #     continue

#     # Define the new path by replacing the base directory
#     relative_path = os.path.relpath(file_path, start=original_base_path)
#     new_file_path = os.path.join(new_directory, relative_path)

#     # Create the new directory structure if it doesn't exist
#     os.makedirs(os.path.dirname(new_file_path), exist_ok=True)

#     # Move the file to the new directory
#     shutil.move(file_path, new_file_path)
#     print(f"Moved {file_path} to {new_file_path}")

# print("All files have been moved successfully.")






#----------------------Less Than 30 Sec Data Separate---------------------->

import os
import shutil
from pydub import AudioSegment

# Function to check audio length
def get_audio_length(file_path):
    audio = AudioSegment.from_wav(file_path)
    return len(audio) / 1000  # Convert milliseconds to seconds

# Function to copy file and maintain directory structure
def copy_file_with_structure(src_file, dest_base_dir):
    dest_file = os.path.join(dest_base_dir, os.path.relpath(src_file, base_dir))
    os.makedirs(os.path.dirname(dest_file), exist_ok=True)
    shutil.copy(src_file, dest_file)
    return dest_file

# Base directory of the dataset
base_dir = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted'

# Destination directory
dest_base_dir = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/filtered_audio_data_test'

# Read the text file
input_file = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/test - Copy.txt'
filtered_rows = []

with open(input_file, 'r', encoding="utf-8") as file:
    for line in file:
        path, transcript = line.strip().split('|')
        file_path = os.path.join(base_dir, path)
        if os.path.exists(file_path):
            audio_length = get_audio_length(file_path)
            if audio_length <= 30:
                dest_file = copy_file_with_structure(file_path, dest_base_dir)
                filtered_rows.append((path, transcript))

# Save the filtered rows to a new text file
output_file = 'D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/quran-splitted/filtered_audio_data_train/filtered_path_transcript_test.txt'
with open(output_file, 'w', encoding="utf-8") as file:
    for path, transcript in filtered_rows:
        file.write(f"{path}|{transcript}\n")

print(f"Filtered data saved to {output_file}")
